# Close Account Solana

Dự án JavaScript để tự động đóng các tài khoản token không sử dụng trên blockchain Solana.

[![GitLab](https://img.shields.io/badge/GitLab-330F63?style=for-the-badge&logo=gitlab&logoColor=white)](https://gitlab.com)
[![Node.js](https://img.shields.io/badge/Node.js-43853D?style=for-the-badge&logo=node.js&logoColor=white)](https://nodejs.org/)
[![Solana](https://img.shields.io/badge/Solana-000000?style=for-the-badge&logo=solana&logoColor=white)](https://solana.com/)

## Tính năng

- Tự động đóng tài khoản token không sử dụng
- Hỗ trợ đốt token trước khi đóng tài khoản
- Thu hồi phí withheld cho token 2022
- <PERSON><PERSON><PERSON><PERSON> lập phí ưu tiên cho giao dịch
- Xử lý tối đa 10 tài khoản mỗi lần

## 🚀 Cài đặt

### Clone từ GitLab
```bash
git clone https://gitlab.com/anhtt121843/close-account-solana.git
cd close-account-solana
npm install
```

### Cài đặt dependencies
```bash
npm install
```

## Sử dụng

### Cách 1: Chạy file JavaScript gốc
```bash
npm start
# hoặc
node close-account.js
```

### Cách 2: Chạy file JavaScript đã được làm sạch
```bash
npm run start:clean
# hoặc
node close-account-clean.js
```

## ⚙️ Cấu hình

### Cách 1: Sử dụng biến môi trường (Khuyến nghị)
Tạo file `.env` từ file `env.example`:

```bash
cp env.example .env
```

Sau đó cập nhật các thông tin trong file `.env`:

```env
HELIUS_API_KEY=your_helius_api_key_here
PRIVATE_KEY=your_private_key_here
PRIORITY_FEE=0.00001
EXCLUDED_TOKENS=token_address_1,token_address_2
```

### Cách 2: Cập nhật trực tiếp trong code
Cập nhật các thông tin sau trong file JavaScript:

```javascript
const privateKey = 'YOUR_PRIVATE_KEY_HERE'; // Private key của ví
const excludedToken = [
    // "TOKEN_ADDRESS_1", // Token không muốn đóng
    // "TOKEN_ADDRESS_2"
];
const priorityFee = 0.00001; // Phí ưu tiên (SOL)
```

## 🔒 Lưu ý bảo mật

- **KHÔNG BAO GIỜ** chia sẻ private key
- **KHÔNG BAO GIỜ** commit private key lên git
- **KHÔNG BAO GIỜ** commit file `.env` chứa thông tin nhạy cảm
- Sử dụng biến môi trường hoặc file config riêng cho production
- File `.env.example` chỉ chứa template, không chứa thông tin thật

## Dependencies

- `@solana/spl-token`: Thao tác với SPL tokens
- `@solana/web3.js`: Kết nối và tương tác với Solana blockchain
- `bs58`: Mã hóa/giải mã Base58
- `bignumber.js`: Xử lý số lớn chính xác

## Cách hoạt động

1. Kết nối với Solana mainnet qua Helius RPC
2. Lấy danh sách tất cả tài khoản token của ví
3. Lọc ra các tài khoản có thể đóng
4. Tạo giao dịch để đóng tối đa 10 tài khoản mỗi lần
5. Lặp lại quá trình này liên tục

## 🚨 Lỗi thường gặp

- **"have no accounts for closing"**: Không có tài khoản nào để đóng
- **"Have no instructions"**: Không thể tạo instruction cho giao dịch
- **Connection error**: Kiểm tra API key và kết nối internet
- **401 Unauthorized**: API key không hợp lệ hoặc đã hết hạn

## 📝 Contributing

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit thay đổi (`git commit -m 'Add some AmazingFeature'`)
4. Push lên branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm chi tiết.

## 🤝 Support

Nếu gặp vấn đề, hãy tạo issue trên GitLab hoặc liên hệ qua:
- Email: <EMAIL>
- Telegram: @your-username
