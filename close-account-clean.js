"use strict";

const { createBurnCheckedInstruction, createHarvestWithheldTokensToMintInstruction, createCloseAccountInstruction } = require("@solana/spl-token");
const { Connection, Keypair, PublicKey, Transaction, ComputeBudgetProgram, sendAndConfirmTransaction } = require("@solana/web3.js");
const BigNumber = require("bignumber.js");
const bs58 = require("bs58").default;

// Kết nối Solana mainnet
const connection = new Connection(
    "https://mainnet.helius-rpc.com/?api-key=3a15d997-1c89-4dc8-ac2b-6b33760835c6", 
    "confirmed"
);

/**
 * Tạo các instruction để thiết lập phí ưu tiên
 */
function buildPriorityFeeIxns(priorityFee, estimatedUnitsConsumed, type) {
    const computeUnits = type ? 500 : Math.round(estimatedUnitsConsumed * 1.1); // tăng 10%
    
    const modifyComputeUnits = ComputeBudgetProgram.setComputeUnitLimit({
        units: computeUnits,
    });
    
    const priceInMicro = new BigNumber(priorityFee)
        .multipliedBy(Math.pow(10, 15)) // micro = 10^-6 lamports, 1 lamport = 10^-9 sol => micro = 10^-15 sol
        .dividedBy(computeUnits)
        .toFixed(0);
    
    const setCuPrice = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: Number(priceInMicro),
    });
    
    return [modifyComputeUnits, setCuPrice];
}

/**
 * Hàm chính để đóng tài khoản token
 */
async function closeAccFunc(privateKey, excludedToken, priorityFee) {
    console.log("\n\n");
    
    // Tạo signer từ private key
    const signer = Keypair.fromSecretKey(bs58.decode(privateKey));
    console.log("public key for this account: ", signer.publicKey);
    
    const transaction = new Transaction();
    
    // Lấy danh sách tài khoản token
    const bata = await connection.getParsedTokenAccountsByOwner(signer.publicKey, {
        programId: new PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), // token
        // programId: new PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), // token 2022
    });
    
    console.log("Number of token accounts: ", bata.value.length);
    
    // Lọc các tài khoản có thể đóng
    console.log("Excluded tokens:", excludedToken);
    const filteredAccs = bata.value.filter(item => {
        const mintAddress = item.account.data.parsed.info.mint;
        console.log("Checking token:", mintAddress);

        if (mintAddress === "So11111111111111111111111111111111111111112") {
            console.log("  -> Skipping SOL wrapper");
            return false;
        }

        if (excludedToken.includes(mintAddress)) {
            console.log("  -> Skipping excluded token:", mintAddress);
            return false;
        }

        if (item.account.data.parsed.info.state === "frozen") {
            console.log("  -> Skipping frozen token");
            return false;
        }

        console.log("  -> Will close this token");
        return true;
    });
    
    console.log("Number of accounts are able to close: ", filteredAccs.length);
    
    // Chỉ xử lý tối đa 10 tài khoản mỗi lần
    const needAccs = filteredAccs.slice(0, 10);
    console.log("Actual number of accounts closing: ", needAccs.length);

    if (needAccs.length === 0) {
        console.log("✅ Không có tài khoản token nào để đóng. Ví đã sạch!");
        throw new Error("WALLET_CLEAN"); // Ném error đặc biệt để dừng vòng lặp
    }

    // Hiển thị danh sách các token sẽ bị đóng để xác nhận
    console.log("\n🚨 CẢnh BÁO: Các token sau sẽ bị đóng:");
    needAccs.forEach((item, index) => {
        const mintAddress = item.account.data.parsed.info.mint;
        const balance = item.account.data.parsed.info.tokenAmount.uiAmount;
        console.log(`${index + 1}. Token: ${mintAddress} - Balance: ${balance}`);
    });
    console.log("\n⚠️  Hãy đảm bảo không có token quan trọng trong danh sách trên!");
    console.log("Script sẽ tiếp tục sau 10 giây...\n");

    // Chờ 10 giây để user có thể dừng script nếu cần
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    const instructions = [];
    
    // Thêm instruction phí ưu tiên nếu cần
    if (priorityFee > 0) {
        instructions.push(...buildPriorityFeeIxns(priorityFee, 100000));
    }
    
    // Tạo các instruction cho từng tài khoản
    needAccs.forEach(item => {
        const { account } = item;
        const { info } = account.data.parsed;
        
        // Đốt token nếu còn số dư
        if (Number(info.tokenAmount.amount) > 0 && 
            info.mint !== "So11111111111111111111111111111111111111112") {
            
            const burnIxn = createBurnCheckedInstruction(
                item.pubkey,
                new PublicKey(info.mint),
                signer.publicKey,
                Number(info.tokenAmount.amount),
                info.tokenAmount.decimals,
                signer.publicKey,
                item.account.owner
            );
            instructions.push(burnIxn);
        }
        
        // Thu hồi phí withheld cho token 2022
        if (info.extensions?.[1]?.state?.withheldAmount > 0) {
            const withdrawFeeIxn = createHarvestWithheldTokensToMintInstruction(
                new PublicKey(info.mint),
                [item.pubkey],
                item.account.owner
            );
            instructions.push(withdrawFeeIxn);
        }
        
        // Đóng tài khoản
        const closeIxn = createCloseAccountInstruction(
            item.pubkey,
            signer.publicKey,
            signer.publicKey,
            [signer.publicKey],
            item.account.owner
        );
        instructions.push(closeIxn);
    });
    
    if (instructions.length === 0) {
        throw new Error("Have no instructions");
    }
    
    // Thêm tất cả instruction vào transaction
    transaction.add(...instructions);
    
    // Gửi và xác nhận transaction
    const signature = await sendAndConfirmTransaction(connection, transaction, [signer]);
    console.log("signature: ", signature);
}

/**
 * Hàm chạy chính
 */
async function run(privateKey, excludedToken, priorityFee) {
    while (true) {
        try {
            await closeAccFunc(privateKey, excludedToken, priorityFee);
            // Nếu không có tài khoản để đóng, chờ 30 giây rồi thử lại
            console.log("⏳ Chờ 30 giây trước khi kiểm tra lại...");
            await new Promise(resolve => setTimeout(resolve, 30000));
        } catch (error) {
            if (error.message === "WALLET_CLEAN") {
                console.log("🎉 Script hoàn thành! Ví đã được dọn sạch.");
                break;
            }
            console.error("❌ Error occurred:", error.message);
            break;
        }
    }
}

// Cấu hình 
const privateKey = '5p9x12c6t8ktZcCY42hc61JXEtnSk8FfMEEb1muaw2NXnpN8KMrn821SGwBVfsKc6DSyjBRMHEuJw375zXihjWfy'; 
// cho địa chỉ account USDC vào đây không là mất hết tiền đấy
const excludedToken = [
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
]; // token addresses separated by a comma
const priorityFee = 0.000001;

// Chạy script
run(privateKey, excludedToken, priorityFee).catch(console.error);

// script runs this tool: node close-account-clean.js
