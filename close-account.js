"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var spl_token_1 = require("@solana/spl-token");
var web3_js_1 = require("@solana/web3.js");
var bignumber_js_1 = require("bignumber.js");
var bs58_1 = require("bs58");
var readline = require('readline');

// Use built-in fetch or require https module for HTTP requests
var https = require('https');

// Simple fetch implementation using https module
function simpleFetch(url, options) {
    return new Promise(function(resolve, reject) {
        var urlObj = new URL(url);
        var requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname + urlObj.search,
            method: (options && options.method) || 'GET',
            headers: (options && options.headers) || {}
        };

        var req = https.request(requestOptions, function(res) {
            var data = '';
            res.on('data', function(chunk) {
                data += chunk;
            });
            res.on('end', function() {
                resolve({
                    json: function() {
                        return Promise.resolve(JSON.parse(data));
                    },
                    text: function() {
                        return Promise.resolve(data);
                    }
                });
            });
        });

        req.on('error', function(error) {
            reject(error);
        });

        if (options && options.body) {
            req.write(options.body);
        }
        req.end();
    });
}
var connection = new web3_js_1.Connection(
// 'https://occupational-bulbs-yjzlpcdxiv-dedicated.helius-rpc.com?api-key=c7e90625-f6fa-4825-8033-df63e4fdc37b',
"https://mainnet.helius-rpc.com/?api-key=3a15d997-1c89-4dc8-ac2b-6b33760835c6", "confirmed");
process.on('SIGINT', function() {
    console.log('\n\n🛑 Script đã được dừng bởi user');
    process.exit(0);
});
function buildPriorityFeeIxns(priorityFee, estimatedUnitsConsumed, type) {
    var computeUnits = type ? 500 : Math.round(estimatedUnitsConsumed * 1.1); // increase by 10%
    var modifyComputeUnits = web3_js_1.ComputeBudgetProgram.setComputeUnitLimit({
        units: computeUnits,
    });
    var priceInMicro = new bignumber_js_1.default(priorityFee)
        .multipliedBy(Math.pow(10, 15)) // micro = 10^-6 lamports, 1 lamport = 10^-9 sol => micro = 10^-15 sol
        .dividedBy(computeUnits)
        .toFixed(0);
    var setCuPrice = web3_js_1.ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: Number(priceInMicro),
    });
    return [modifyComputeUnits, setCuPrice];
}

// Function để swap token qua Jupiter DEX
function swapTokenToSol(tokenMint, amount, decimals, signer) {
    return __awaiter(this, void 0, void 0, function () {
        var quoteResponse, quoteData, swapResponse, swapData, swapTransaction, signature, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 7, , 8]);
                    console.log("🔄 Đang thử swap " + tokenMint + " sang SOL...");
                    return [4 /*yield*/, simpleFetch("https://quote-api.jup.ag/v6/quote?inputMint=" + tokenMint + "&outputMint=So11111111111111111111111111111111111111112&amount=" + amount + "&slippageBps=300")];
                case 1:
                    quoteResponse = _a.sent();
                    return [4 /*yield*/, quoteResponse.json()];
                case 2:
                    quoteData = _a.sent();
                    if (!quoteData.routePlan || quoteData.error) {
                        console.log("❌ Không tìm thấy route để swap " + tokenMint);
                        return [2 /*return*/, false];
                    }
                    return [4 /*yield*/, simpleFetch('https://quote-api.jup.ag/v6/swap', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                quoteResponse: quoteData,
                                userPublicKey: signer.publicKey.toString(),
                                wrapAndUnwrapSol: true,
                            })
                        })];
                case 3:
                    swapResponse = _a.sent();
                    return [4 /*yield*/, swapResponse.json()];
                case 4:
                    swapData = _a.sent();
                    if (swapData.error) {
                        console.log("❌ Lỗi tạo swap transaction: " + swapData.error);
                        return [2 /*return*/, false];
                    }
                    swapTransaction = web3_js_1.Transaction.from(Buffer.from(swapData.swapTransaction, 'base64'));
                    return [4 /*yield*/, (0, web3_js_1.sendAndConfirmTransaction)(connection, swapTransaction, [signer])];
                case 5:
                    signature = _a.sent();
                    console.log("✅ Swapped " + tokenMint + " - signature: " + signature);
                    return [2 /*return*/, true];
                case 6:
                    error_1 = _a.sent();
                    console.log("❌ Swap failed for " + tokenMint + ": " + error_1.message);
                    return [2 /*return*/, false];
                case 7: return [2 /*return*/];
            }
        });
    });
}

// Function để xử lý swap tất cả tokens
function processTokenSwaps(needAccs, signer) {
    return __awaiter(this, void 0, void 0, function () {
        var swapResults, i, item, tokenInfo, swapSuccess;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    swapResults = {
                        successful: [],
                        failed: []
                    };
                    console.log("\n🔄 PHASE 1: Đang thử swap tokens sang SOL...");
                    i = 0;
                    _a.label = 1;
                case 1:
                    if (!(i < needAccs.length)) return [3 /*break*/, 4];
                    item = needAccs[i];
                    tokenInfo = item.account.data.parsed.info;
                    // Skip nếu không có balance hoặc là SOL wrapper
                    if (Number(tokenInfo.tokenAmount.amount) === 0 ||
                        tokenInfo.mint === "So11111111111111111111111111111111111111112") {
                        swapResults.failed.push({ item: item, reason: "No balance or SOL wrapper" });
                        return [3 /*break*/, 3];
                    }
                    return [4 /*yield*/, swapTokenToSol(tokenInfo.mint, tokenInfo.tokenAmount.amount, tokenInfo.tokenAmount.decimals, signer)];
                case 2:
                    swapSuccess = _a.sent();
                    if (swapSuccess) {
                        swapResults.successful.push({ item: item });
                    } else {
                        swapResults.failed.push({ item: item, reason: "Swap failed" });
                    }
                    _a.label = 3;
                case 3:
                    i++;
                    return [3 /*break*/, 1];
                case 4: return [2 /*return*/, swapResults];
            }
        });
    });
}

// Function để hiển thị kết quả swap và xác nhận burn
function showSwapResultsAndConfirm(swapResults) {
    return __awaiter(this, void 0, void 0, function () {
        var rl;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log("\n📊 KẾT QUẢ SWAP:");
                    console.log("✅ Swapped thành công: " + swapResults.successful.length + " tokens");
                    console.log("❌ Swap thất bại: " + swapResults.failed.length + " tokens");

                    if (swapResults.failed.length > 0) {
                        console.log("\n🔥 PHASE 2: Các token sau sẽ bị BURN (không thể swap):");
                        swapResults.failed.forEach(function(failedToken, index) {
                            var tokenInfo = failedToken.item.account.data.parsed.info;
                            console.log((index + 1) + ". Token: " + tokenInfo.mint + " - Balance: " + tokenInfo.tokenAmount.uiAmount + " - Lý do: " + failedToken.reason);
                        });

                        rl = readline.createInterface({
                            input: process.stdin,
                            output: process.stdout
                        });

                        return [4 /*yield*/, new Promise(function(resolve, reject) {
                            rl.question('\n⚠️  Tiếp tục burn các token không swap được? (y/n): ', function(answer) {
                                rl.close();
                                if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                                    resolve();
                                } else {
                                    reject(new Error('USER_CANCELLED_BURN'));
                                }
                            });
                        })];
                    }
                case 1:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    });
}
var closeAccFunc = function (privateKey, excludedToken, priorityFee) { return __awaiter(void 0, void 0, void 0, function () {
    var signer, transaction, bata, filteredAccs, needAccs, swapResults, instructions, signature;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                console.log("\n\n");
                signer = web3_js_1.Keypair.fromSecretKey(bs58_1.default.decode(privateKey));
                console.log("public key for this account: ", signer.publicKey);
                transaction = new web3_js_1.Transaction();
                return [4 /*yield*/, connection.getParsedTokenAccountsByOwner(signer.publicKey, {
                        programId: new web3_js_1.PublicKey("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), // token
                        // programId: new PublicKey("TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb"), // token 2022
                    })];
            case 1:
                bata = _a.sent();
                console.log("Number of token accounts: ", bata.value.length);
                filteredAccs = bata.value.filter(function (item) {
                    if (item.account.data.parsed.info.mint ==
                        "So11111111111111111111111111111111111111112" ||
                        excludedToken.includes(item.account.data.parsed.info.mint) ||
                        item.account.data.parsed.info.state == "frozen") {
                        return false;
                    }
                    return true;
                });
                console.log("Number of accounts are able to close: ", filteredAccs.length);
                needAccs = filteredAccs.slice(0, 10);
                console.log("Actual number of accounts closing: ", needAccs.length);
                if (needAccs.length == 0) {
                    console.log("✅ Không có tài khoản token nào để đóng. Ví đã sạch!");
                    throw new Error("WALLET_CLEAN"); // Ném error đặc biệt để dừng vòng lặp
                }

                // Hiển thị danh sách các token sẽ bị đóng để xác nhận
                console.log("\n🚨 CẢNH BÁO: Các token sau sẽ bị đóng:");
                needAccs.forEach(function(item, index) {
                    var mintAddress = item.account.data.parsed.info.mint;
                    var balance = item.account.data.parsed.info.tokenAmount.uiAmount;
                    console.log((index + 1) + ". Token: " + mintAddress + " - Balance: " + balance);
                });
                console.log("\n⚠️  Hãy đảm bảo không có token quan trọng trong danh sách trên!");
                console.log("Nhấn 'y' để tiếp tục hoặc 'n' để dừng:");

                var rl = readline.createInterface({
                    input: process.stdin,
                    output: process.stdout
                });

                return [4 /*yield*/, new Promise(function(resolve, reject) {
                    rl.question('Tiếp tục? (y/n): ', function(answer) {
                        rl.close();
                        if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                            resolve();
                        } else {
                            reject(new Error('USER_CANCELLED'));
                        }
                    });
                })];
            case 2:
                _a.sent(); // User đã confirm

                // PHASE 1: Xử lý swap tokens trước
                return [4 /*yield*/, processTokenSwaps(needAccs, signer)];
            case 3:
                swapResults = _a.sent();

                // PHASE 2: Hiển thị kết quả và hỏi user về burn
                if (swapResults.failed.length > 0) {
                    return [4 /*yield*/, showSwapResultsAndConfirm(swapResults)];
                } else {
                    console.log("\n🎉 Tất cả tokens đã được swap thành công!");
                }
                return [3 /*break*/, 5];
            case 4:
                _a.sent();
                _a.label = 5;
            case 5:

                instructions = [];
                if (priorityFee > 0) {
                    instructions.push.apply(instructions, buildPriorityFeeIxns(priorityFee, 100000));
                }

                // PHASE 3: Burn các token swap thất bại
                swapResults.failed.forEach(function(failedToken) {
                    var item = failedToken.item;
                    var tokenInfo = item.account.data.parsed.info;

                    // Burn token nếu còn số dư
                    if (Number(tokenInfo.tokenAmount.amount) > 0 &&
                        tokenInfo.mint !== "So11111111111111111111111111111111111111112") {

                        var burnIxn = (0, spl_token_1.createBurnCheckedInstruction)(
                            item.pubkey,
                            new web3_js_1.PublicKey(tokenInfo.mint),
                            signer.publicKey,
                            Number(tokenInfo.tokenAmount.amount),
                            tokenInfo.tokenAmount.decimals,
                            signer.publicKey,
                            item.account.owner
                        );
                        instructions.push(burnIxn);
                    }
                });

                // PHASE 4: Close account cho TẤT CẢ tokens (cả swap thành công và thất bại)
                needAccs.forEach(function(item) {
                    var _a, _b, _c, _d;
                    
                    // Token 2022 withheld amount
                    if (((_d = (_c = (_b = (_a = item.account.data.parsed.info) === null || _a === void 0 ? void 0 : _a.extensions) === null || _b === void 0 ? void 0 : _b[1]) === null || _c === void 0 ? void 0 : _c.state) === null || _d === void 0 ? void 0 : _d.withheldAmount) > 0) {
                        var withdrawFeeIxn = (0, spl_token_1.createHarvestWithheldTokensToMintInstruction)(
                            new web3_js_1.PublicKey(item.account.data.parsed.info.mint), 
                            [item.pubkey], 
                            item.account.owner
                        );
                        instructions.push(withdrawFeeIxn);
                    }
                    
                    var closeIxn = (0, spl_token_1.createCloseAccountInstruction)(
                        item.pubkey, 
                        signer.publicKey, 
                        signer.publicKey, 
                        [signer.publicKey], 
                        item.account.owner
                    );
                    instructions.push(closeIxn);
                });

                if (instructions.length == 0) {
                    throw new Error("Have no instructions");
                }
                transaction.add.apply(transaction, instructions);
                return [4 /*yield*/, (0, web3_js_1.sendAndConfirmTransaction)(connection, transaction, [
                        signer,
                    ])];
            case 6:
                signature = _a.sent();
                console.log("signature: ", signature);
                return [2 /*return*/];
        }
    });
}); };
var run = function (privateKey, excludedToken, priorityFee) { return __awaiter(void 0, void 0, void 0, function () {
    var error_1;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0:
                if (!true) return [3 /*break*/, 7];
                _a.label = 1;
            case 1:
                _a.trys.push([1, 5, , 6]);
                return [4 /*yield*/, closeAccFunc(privateKey, excludedToken, priorityFee)];
            case 2:
                _a.sent();
                // Nếu không có tài khoản để đóng, chờ 3 giây rồi thử lại
                console.log("⏳ Chờ 3 giây trước khi kiểm tra lại...");
                return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, 3000); })];
            case 3:
                _a.sent();
                return [3 /*break*/, 6];
            case 4: return [3 /*break*/, 0];
            case 5:
                error_1 = _a.sent();
                if (error_1.message === "WALLET_CLEAN") {
                    console.log("🎉 Script hoàn thành! Ví đã được dọn sạch.");
                    return [3 /*break*/, 7];
                }
                if (error_1.message === "USER_CANCELLED") {
                    console.log("🛑 User đã hủy quá trình đóng tài khoản.");
                    return [3 /*break*/, 7];
                }
                if (error_1.message === "USER_CANCELLED_BURN") {
                    console.log("🛑 User đã hủy quá trình burn tokens.");
                    return [3 /*break*/, 7];
                }
                console.error("❌ Error occurred:", error_1.message);
                return [3 /*break*/, 7];
            case 6: return [3 /*break*/, 0];
            case 7: return [2 /*return*/];
        }
    });
}); };



 // điền private key vào đây
//var privateKey = 'uZHqUSGydrMKAN9dKBAfxVEZhZgaLKpTqBUBNJCSzdiNLaSRc94hCEaS78tVQuzYQXQZecT9Np4urYtKVFTpt9e' // YUN
//var privateKey = '4vfTMVjx1mVRuBodbnm5sAd87Sq35xbgqHgwPqrUX4Cb6nZbiqW3Dr9BcDfjBVp3q7tp1K5HhyGo814jkxTJ8zYi'; // H1j8
var privateKey = '3pmZXHKDejsrHmGZMkzZdBqEaQo7B2MVQoLgUx7cc1xxxaHCSA3wGZUQdSLeAfnW4bBrgaWufK711LjAw1QPjBUd'; // 8Qpq
// không đóng  các token này
var excludedToken = [
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC không đóng Token này để tránh mất USDC
    "XsoCS1TfEyfFhfvj8EtZ528L3CaKBDBRqRapnBbDF2W",
    //"J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn",
]; // địa chỉ token cách nhau bằng dấu phẩy
var priorityFee = 0.000001; // phí ưu tiên gửi lệnh đóng tài khoản
run(privateKey, excludedToken, priorityFee);
// script runs this tool: npx ts-node close-account.js
